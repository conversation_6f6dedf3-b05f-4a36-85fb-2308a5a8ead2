import type { CellRendererProps } from '@/types';
import { isValidNumber } from './utils';

export interface FormatNumberOptions {
  /** 保留的小数位数，默认为2 */
  fix?: number;
  /** 是否转换为百分比格式，默认为false */
  percent?: boolean;
  /** 是否添加正负号前缀，默认为false */
  prefix?: boolean;
  /** 是否去掉末尾多余的0，默认为false */
  trim?: boolean;
  /** 默认值，默认为空字符串 */
  default?: string;
  /**是否缩写大数字(万/亿)，默认为false */
  abbreviate?: boolean;
  /** 是否添加千位分隔符，默认为false */
  separator?: boolean;
}

/**
 * to format date time(can be Date object, UTC timestamp, date time string) with a given pattern
 * @param source input value to be formatted
 * @param pattern output pattern string(by default = 'yyyy-MM-dd hh:mm:ss')
 */
export const formatDateTime = (source: Date | number | string, pattern?: string): string => {
  if (!source || source == '--') {
    return '--';
  }

  /**
   * [yyyyMMdd] formatter is commonly used across the web
   */
  if (typeof source == 'number' && source.toString().length == 8) {
    source = source.toString();
  }

  if (typeof source == 'string' && /^\d{8}$/.test(source)) {
    source = `${source.substring(0, 4)}/${source.substring(4, 6)}/${source.substring(6, 8)}`;
  }

  /**
   * the minimal length is [yyyyMMdd]/8
   */
  if (typeof source == 'string' && source.length < 8) {
    return source;
  }

  if (pattern == undefined || pattern == null) {
    pattern = 'yyyy-MM-dd hh:mm:ss';
  }

  const dt = source instanceof Date ? source : new Date(source);
  const o: any = {
    'M+': dt.getMonth() + 1,
    'd+': dt.getDate(),
    'h+': dt.getHours(),
    'm+': dt.getMinutes(),
    's+': dt.getSeconds(),
    'q+': Math.floor((dt.getMonth() + 3) / 3),
    S: dt.getMilliseconds(),
  };

  if (/(y+)/.test(pattern)) {
    pattern = pattern.replace(RegExp.$1, (dt.getFullYear() + '').substr(4 - RegExp.$1.length));
  }

  for (const k in o) {
    if (new RegExp('(' + k + ')').test(pattern)) {
      pattern = pattern.replace(
        RegExp.$1,
        RegExp.$1.length == 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length),
      );
    }
  }

  return pattern;
};

/**
 * 渲染枚举类型文本
 * @param value 值
 * @param enums 枚举值列表
 */
export const renderLabel = (
  value: unknown,
  enums:
    | { label: string; value: unknown }[]
    | { Label: string; Value: unknown }[]
    | [key: string, value: unknown][],
): string | unknown => {
  if (!Array.isArray(enums)) {
    return value;
  }
  const ismatrix = enums[0] instanceof Array;
  if (ismatrix) {
    const matched = (enums as [string, unknown][]).find(x => x[1] === value);
    return matched ? matched[0] : value || '--';
  } else {
    const islower = 'label' in enums[0];
    if (islower) {
      return (
        (enums as { label: string; value: unknown }[]).find(x => x.value === value)?.label ||
        (value === null || value === undefined ? '--' : value)
      );
    } else {
      return (
        (enums as { Label: string; Value: unknown }[]).find(x => x.Value === value)?.Label ||
        (value === null || value === undefined ? '--' : value)
      );
    }
  }
};

/**
 * 格式化数字为字符串
 *
 * @param num 要格式化的数字
 * @param options 格式化选项
 * @param options.fix 保留的小数位数，默认为2
 * @param options.percent 是否转换为百分比格式，默认为false
 * @param options.prefix 是否添加正负号前缀，默认为false
 * @param options.trim 是否去掉末尾多余的0，默认为false
 * @param options.abbreviate 是否缩写大数字(万/亿)，默认为false
 * @param options.separator 是否添加千位分隔符，默认为false
 * @param options.default 默认值，默认为空字符串
 * @returns 格式化后的字符串
 *
 * @example
 * // 基础用法
 * formatNumber(12.345) // "12.35"
 *
 * // 百分比格式
 * formatNumber(0.1234, { percent: true }) // "12.34%"
 *
 * // 添加正负号
 * formatNumber(-12.34, { prefix: true }) // "-12.34"
 * formatNumber(12.34, { prefix: true }) // "+12.34"
 *
 * // 保留末尾的0
 * formatNumber(12.3, { trim: false }) // "12.30"
 *
 * // 数字缩写
 * formatNumber(12345, { abbreviate: true }) // "1.23万"
 * formatNumber(123456789, { abbreviate: true }) // "1.23亿"
 *
 * // 添加千位分隔符
 * formatNumber(5123456.789, { separator: true }) // "5,123,456.79"
 */
export const formatNumber = (num: number | unknown, options?: FormatNumberOptions): string => {
  const {
    fix = 2,
    percent = false,
    prefix = false,
    trim = false,
    abbreviate = false,
    separator = false,
  } = options || {};

  if (!isValidNumber(num)) {
    return options?.default || '';
  }

  let result = num as number;
  if (percent) {
    result *= 100;
  }

  // 处理数字缩写
  let unit = '';
  if (abbreviate) {
    if (Math.abs(result) >= 100000000) {
      result = result / 100000000;
      unit = '亿';
    } else if (Math.abs(result) >= 10000) {
      result = result / 10000;
      unit = '万';
    }
  }

  let formatted = result.toFixed(fix);
  if (trim) {
    formatted = parseFloat(formatted).toString();
  }

  // 添加千位分隔符
  if (separator) {
    const parts = formatted.split('.');
    parts[0] = parts[0].replace(/\B(?=(\d{3})+(?!\d))/g, ',');
    formatted = parts.join('.');
  }

  // 添加单位
  if (unit) {
    formatted += unit;
  }

  if (percent) {
    formatted += '%';
  }

  if (prefix) {
    if (result > 0) {
      formatted = '+' + formatted;
    } else if (result < 0) {
      formatted = '-' + formatted.slice(1);
    }
  }

  return formatted;
};

// 快捷方法

/**
 * 格式化数字为百分比
 *
 * @param num 要格式化的数字（小数形式，如0.1234表示12.34%）
 * @returns 格式化后的百分比字符串
 *
 * @example
 * formatPercent(0.1234) // "12.34%"
 * formatPercent(-0.05) // "-5.00%"
 */
export const formatPercent = (num: number | unknown) => {
  return formatNumber(num, { percent: true });
};

/**
 * 格式化数字为带正负号的字符串
 *
 * @param num 要格式化的数字
 * @returns 格式化后的字符串
 *
 * @example
 * formatWithSign(12.34) // "+12.34"
 * formatWithSign(-12.34) // "-12.34"
 * formatWithSign(0) // "0.00"
 */
export const formatWithSign = (num: number | unknown) => {
  return formatNumber(num, { prefix: true });
};

/**
 * 格式化数字为千位分隔符格式
 *
 * @param num 要格式化的数字
 * @returns 格式化后的字符串
 *
 * @example
 * thousands(1234567.89) // "1,234,567.89"
 */
export const thousands = (num: number | unknown) => {
  return formatNumber(num, { separator: true });
};

/**
 * 格式化数值为千分位（整数）
 */
export const thousandsInt = (number: number): string | number => {
  return formatNumber(number, { fix: 0, separator: true });
};

/**
 * 格式化数字为缩写格式（万/亿）
 *
 * @param num 要格式化的数字
 * @returns 格式化后的字符串
 *
 * @example
 * formatAbbreviated(12345) // "1.23万"
 * formatAbbreviated(123456789) // "1.23亿"
 */
export const formatAbbreviated = (num: number | unknown) => {
  return formatNumber(num, { abbreviate: true });
};

/**
 * 格式化整数（无小数位）
 *
 * @param num 要格式化的数字
 * @returns 格式化后的字符串
 *
 * @example
 * formatInteger(12.89) // "13"
 */
export const formatInteger = (num: number | unknown) => {
  return formatNumber(num, { fix: 0 });
};

/**
 * 表格列渲染千分位数值, 带tooltip
 */
export function thousandsCol<T, K extends keyof T>(params: CellRendererProps<T, K>) {
  const result = thousands(params.cellData);
  return (
    <span class="toe" title={result}>
      {result}
    </span>
  );
}

/**
 * 表格列渲染百分比, 带tooltip
 */
export function percentageCol<T, K extends keyof T>(params: CellRendererProps<T, K>) {
  const value = formatPercent(params.cellData);
  return (
    <span class="toe" title={value}>
      {value}
    </span>
  );
}

/**
 * 表格列渲染净值, 带tooltip
 */
export function navCol<T, K extends keyof T>(params: CellRendererProps<T, K>) {
  const value = formatNumber(params.cellData, { fix: 4 });
  return (
    <span class="toe" title={value}>
      {value}
    </span>
  );
}

/**
 * 根据数值返回对应的颜色样式类名
 *
 * @param val - 要判断的值，可以是任意类型
 * @param options - 可选配置项
 * @param options.defaultClass - 当值为基准值时使用的默认样式类名，默认为'text-white'
 * @param options.prefix - 样式类名前缀，默认为'c'
 * @param options.baseline - 判断颜色的基准值，默认为0
 * @returns 返回对应的颜色样式类名
 *
 * @example
 * // 基础用法
 * getColorClass(100) // 返回 "c-[var(--g-red)]"
 * getColorClass(-50) // 返回 "c-[var(--g-green)]"
 * getColorClass(0)  // 返回 "text-white"
 *
 * // 自定义前缀
 * getColorClass(100, { prefix: 'bg' }) // 返回 "bg-[var(--g-red)]"
 *
 * // 自定义默认类名
 * getColorClass(0, { defaultClass: 'text-gray' }) // 返回 "text-gray"
 *
 * // 自定义基准值
 * getColorClass(80, { baseline: 100 }) // 返回 "c-[var(--g-green)]"
 */
export const getColorClass = (
  val: unknown,
  options?: { defaultClass?: string; prefix?: string; baseline?: number },
) => {
  const { defaultClass = 'text-white', prefix = 'c', baseline = 0 } = options || {};
  if (!isValidNumber(val)) {
    return '';
  } else {
    if (val === baseline) {
      return defaultClass;
    }
    const color = (val as number) > baseline ? 'red' : 'green';
    return `${prefix}-[var(--g-${color})]`;
  }
};
