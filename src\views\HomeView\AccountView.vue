<script setup lang="ts">
import AccountList from '@/components/AccountView/AccountList.vue';
import ComponentTabs from '@/components/common/ComponentTabs.vue';
import type { ComponentTab } from '@/types';
import { computed, ref } from 'vue';
import type { LegacyAccountInfo } from '../../../../xtrade-sdk/dist';

const activeAccount = ref<LegacyAccountInfo | null>(null);

const tabs = computed<ComponentTab[]>(() => [
  {
    label: '当日订单',
    component: 'CommonOrders',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '当日持仓',
    component: 'CommonPositions',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '当日成交',
    component: 'CommonRecords',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '历史订单',
    component: 'HistoryOrders',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '历史持仓',
    component: 'HistoryPositions',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '历史成交',
    component: 'HistoryRecords',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '历史权益',
    component: 'HistoryEquity',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
  {
    label: '出入金',
    component: 'FundTransfer',
    props: {
      activeItem: activeAccount.value,
      type: 'account',
    },
  },
]);

const handleRowClick = (row: LegacyAccountInfo) => {
  activeAccount.value = row;
};

// 处理账号列表加载完成，默认选中第一行
const handleAccountsLoaded = (accounts: LegacyAccountInfo[]) => {
  if (accounts.length > 0 && !activeAccount.value) {
    activeAccount.value = accounts[0];
  }
};
</script>

<template>
  <div>
    <el-splitter layout="vertical">
      <el-splitter-panel>
        <AccountList h-full @row-click="handleRowClick" @accounts-loaded="handleAccountsLoaded" />
      </el-splitter-panel>
      <el-splitter-panel>
        <ComponentTabs :tabs flex-1 min-h-1 />
      </el-splitter-panel>
    </el-splitter>
  </div>
</template>

<style scoped></style>
