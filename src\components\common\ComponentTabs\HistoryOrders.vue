<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import { OrderStatusEnum, PositionEffectEnum, TradeDirectionEnum } from '@/enum';
import { getColorClass } from '@/script/formatter';
import type { LegacyFundInfo, OrderInfo } from '../../../../../xtrade-sdk';

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
}>();

// 基础列定义
const baseColumns: ColumnDefinition<OrderInfo> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '代码',
    width: 80,
    sortable: true,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '名称',
    width: 100,
    sortable: true,
  },
  {
    key: 'direction',
    dataKey: 'direction',
    title: '方向',
    width: 60,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      const dirText = TradeDirectionEnum[cellData];
      return <span class={getColorClass(cellData)}>{dirText}</span>;
    },
  },
  {
    key: 'positionEffect',
    dataKey: 'positionEffect',
    title: '开平',
    width: 60,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      return <span>{PositionEffectEnum[cellData]}</span>;
    },
  },
  {
    key: 'orderPrice',
    dataKey: 'orderPrice',
    title: '委托价',
    width: 80,
    sortable: true,
    align: 'right',
  },
  {
    key: 'volumeOriginal',
    dataKey: 'volumeOriginal',
    title: '委托量',
    width: 80,
    sortable: true,
    align: 'right',
  },
  {
    key: 'tradedVolume',
    dataKey: 'tradedVolume',
    title: '成交量',
    width: 80,
    sortable: true,
    align: 'right',
  },
  {
    key: 'tradedPrice',
    dataKey: 'tradedPrice',
    title: '成交价',
    width: 80,
    sortable: true,
    align: 'right',
  },
  {
    key: 'orderStatus',
    dataKey: 'orderStatus',
    title: '状态',
    width: 100,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      const colorClass = getOrderStatusColor(cellData as number);
      return <span class={colorClass}>{OrderStatusEnum[cellData]}</span>;
    },
  },
  {
    key: 'orderTime',
    dataKey: 'orderTime',
    title: '委托时间',
    width: 160,
    sortable: true,
  },
  {
    key: 'tradingDay',
    dataKey: 'tradingDay',
    title: '交易日',
    width: 100,
    sortable: true,
  },
];

// 产品特有的列
const productColumn: ColumnDefinition<OrderInfo>[0] = {
  key: 'accountName',
  dataKey: 'accountName',
  title: '账户',
  width: 120,
  sortable: true,
};

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.push(productColumn);
  }
  return cols;
});

// 历史订单数据
const historyOrders = shallowRef<OrderInfo[]>([]);
const tableRef = useTemplateRef('tableRef');

// 日期范围
const dateRange = shallowRef<[Date, Date] | null>(null);

// 获取订单状态颜色
const getOrderStatusColor = (value: number): string => {
  switch (value) {
    case OrderStatusEnum.全成:
      return 'c-[var(--g-green)]';
    case OrderStatusEnum.已撤:
    case OrderStatusEnum.已驳回:
    case OrderStatusEnum.废单:
      return 'c-[var(--g-red)]';
    case OrderStatusEnum.部分成交:
    case OrderStatusEnum.部分成交撤单:
      return 'c-[var(--g-orange)]';
    default:
      return 'c-[var(--g-white)]';
  }
};

// 获取历史订单数据
const fetchHistoryOrders = async () => {
  if (!activeItem) return;

  // 模拟历史订单数据
  const mockData: OrderInfo[] = [
    {
      id: 1001,
      instrument: '600036',
      instrumentName: '招商银行',
      direction: TradeDirectionEnum.买入,
      positionEffect: PositionEffectEnum.开仓,
      orderPrice: 31.5,
      volumeOriginal: 1000,
      tradedVolume: 1000,
      tradedPrice: 31.48,
      orderStatus: OrderStatusEnum.全成,
      orderTime: '2024-01-15 09:30:00',
      tradingDay: '2024-01-15',
      accountName: type === 'product' ? '测试账户1' : undefined,
    },
    {
      id: 1002,
      instrument: '000001',
      instrumentName: '平安银行',
      direction: TradeDirectionEnum.卖出,
      positionEffect: PositionEffectEnum.平仓,
      orderPrice: 12.8,
      volumeOriginal: 500,
      tradedVolume: 0,
      tradedPrice: 0,
      orderStatus: OrderStatusEnum.已撤,
      orderTime: '2024-01-14 14:20:00',
      tradingDay: '2024-01-14',
      accountName: type === 'product' ? '测试账户1' : undefined,
    },
  ];

  historyOrders.value = mockData;
};

// 导出数据
const exportData = () => {
  // 实现导出功能
  console.log('导出历史订单数据');
};

onMounted(() => {
  if (activeItem) {
    fetchHistoryOrders();
  }
});

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchHistoryOrders();
    }
  },
  { deep: true },
);

// 监听日期范围变化
watch(dateRange, () => {
  fetchHistoryOrders();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'orderTime', order: TableV2SortOrder.DESC }"
    :columns
    :data="historyOrders"
    fixed
  >
    <template #left>
      <div flex aic>
        <span mr-8 text-sm>日期范围:</span>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          w-240
          mr-16
        />
      </div>
    </template>
    <template #actions>
      <div class="actions" flex aic>
        <el-button @click="fetchHistoryOrders" size="small" color="var(--g-primary)">
          查询
        </el-button>
        <el-button @click="exportData" size="small">导出</el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
