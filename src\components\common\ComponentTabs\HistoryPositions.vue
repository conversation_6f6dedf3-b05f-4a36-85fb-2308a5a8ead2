<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import { PositionDirectionEnum } from '@/enum';
import { formatNumber, getColorClass } from '@/script/formatter';
import type { LegacyFundInfo, PositionInfo } from '../../../../../xtrade-sdk';

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
}>();

// 基础列定义
const baseColumns: ColumnDefinition<PositionInfo> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '代码',
    width: 80,
    sortable: true,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '名称',
    width: 100,
    sortable: true,
  },
  {
    key: 'direction',
    dataKey: 'direction',
    title: '方向',
    width: 60,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      const dirText = PositionDirectionEnum[cellData];
      return <span class={getColorClass(cellData)}>{dirText}</span>;
    },
  },
  {
    key: 'position',
    dataKey: 'position',
    title: '持仓量',
    width: 80,
    sortable: true,
    align: 'right',
  },
  {
    key: 'avgPrice',
    dataKey: 'avgPrice',
    title: '均价',
    width: 80,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => formatNumber(cellData, 2),
  },
  {
    key: 'lastPrice',
    dataKey: 'lastPrice',
    title: '最新价',
    width: 80,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => formatNumber(cellData, 2),
  },
  {
    key: 'marketValue',
    dataKey: 'marketValue',
    title: '市值',
    width: 100,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => formatNumber(cellData, 2),
  },
  {
    key: 'positionPnL',
    dataKey: 'positionPnL',
    title: '持仓盈亏',
    width: 100,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => {
      const colorClass =
        cellData > 0
          ? 'c-[var(--g-green)]'
          : cellData < 0
            ? 'c-[var(--g-red)]'
            : 'c-[var(--g-white)]';
      return <span class={colorClass}>{formatNumber(cellData, 2)}</span>;
    },
  },
  {
    key: 'positionPnLRatio',
    dataKey: 'positionPnLRatio',
    title: '盈亏比例',
    width: 100,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => {
      const colorClass =
        cellData > 0
          ? 'c-[var(--g-green)]'
          : cellData < 0
            ? 'c-[var(--g-red)]'
            : 'c-[var(--g-white)]';
      return <span class={colorClass}>{(cellData * 100).toFixed(2)}%</span>;
    },
  },
  {
    key: 'tradingDay',
    dataKey: 'tradingDay',
    title: '交易日',
    width: 100,
    sortable: true,
  },
];

// 产品特有的列
const productColumn: ColumnDefinition<PositionInfo>[0] = {
  key: 'accountName',
  dataKey: 'accountName',
  title: '账户',
  width: 120,
  sortable: true,
};

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.push(productColumn);
  }
  return cols;
});

// 历史持仓数据
const historyPositions = shallowRef<PositionInfo[]>([]);
const tableRef = useTemplateRef('tableRef');

// 日期范围
const dateRange = shallowRef<[Date, Date] | null>(null);

// 获取历史持仓数据
const fetchHistoryPositions = async () => {
  if (!activeItem) return;

  // 模拟历史持仓数据
  const mockData: PositionInfo[] = [
    {
      id: 1,
      instrument: '600036',
      instrumentName: '招商银行',
      direction: PositionDirectionEnum.多头,
      position: 1000,
      avgPrice: 31.5,
      lastPrice: 32.1,
      marketValue: 32100,
      positionPnL: 600,
      positionPnLRatio: 0.019,
      tradingDay: '2024-01-15',
      accountName: type === 'product' ? '测试账户1' : undefined,
    },
    {
      id: 2,
      instrument: '000001',
      instrumentName: '平安银行',
      direction: PositionDirectionEnum.多头,
      position: 500,
      avgPrice: 12.8,
      lastPrice: 12.65,
      marketValue: 6325,
      positionPnL: -75,
      positionPnLRatio: -0.012,
      tradingDay: '2024-01-14',
      accountName: type === 'product' ? '测试账户1' : undefined,
    },
  ];

  historyPositions.value = mockData;
};

// 导出数据
const exportData = () => {
  // 实现导出功能
  console.log('导出历史持仓数据');
};

onMounted(() => {
  if (activeItem) {
    fetchHistoryPositions();
  }
});

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchHistoryPositions();
    }
  },
  { deep: true },
);

// 监听日期范围变化
watch(dateRange, () => {
  fetchHistoryPositions();
});
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :sort="{ key: 'tradingDay', order: TableV2SortOrder.DESC }"
    :columns
    :data="historyPositions"
    identity="instrument"
    fixed
  >
    <template #left>
      <div flex aic>
        <span mr-8 text-sm>日期范围:</span>
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          w-240
          mr-16
        />
      </div>
    </template>
    <template #actions>
      <div class="actions" flex aic>
        <el-button @click="fetchHistoryPositions" size="small" color="var(--g-primary)">
          查询
        </el-button>
        <el-button @click="exportData" size="small">导出</el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
