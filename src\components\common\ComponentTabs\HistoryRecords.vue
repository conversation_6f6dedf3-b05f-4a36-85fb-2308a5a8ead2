<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import { PositionEffectEnum, TradeDirectionEnum } from '@/enum';
import { formatNumber, getColorClass } from '@/script/formatter';
import type { LegacyFundInfo, TradeRecordInfo } from '../../../../../xtrade-sdk';

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
}>();

// 基础列定义
const baseColumns: ColumnDefinition<TradeRecordInfo> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '代码',
    width: 80,
    sortable: true,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '名称',
    width: 100,
    sortable: true,
  },
  {
    key: 'direction',
    dataKey: 'direction',
    title: '方向',
    width: 60,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      const dirText = TradeDirectionEnum[cellData];
      return <span class={getColorClass(cellData)}>{dirText}</span>;
    },
  },
  {
    key: 'positionEffect',
    dataKey: 'positionEffect',
    title: '开平',
    width: 60,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      return <span>{PositionEffectEnum[cellData]}</span>;
    },
  },
  {
    key: 'volume',
    dataKey: 'volume',
    title: '成交量',
    width: 80,
    sortable: true,
    align: 'right',
  },
  {
    key: 'tradedPrice',
    dataKey: 'tradedPrice',
    title: '成交价',
    width: 80,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => formatNumber(cellData, 2),
  },
  {
    key: 'tradedAmount',
    dataKey: 'tradedAmount',
    title: '成交金额',
    width: 100,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => formatNumber(cellData, 2),
  },
  {
    key: 'commission',
    dataKey: 'commission',
    title: '手续费',
    width: 80,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => formatNumber(cellData, 2),
  },
  {
    key: 'tradeTime',
    dataKey: 'tradeTime',
    title: '成交时间',
    width: 160,
    sortable: true,
  },
  {
    key: 'tradingDay',
    dataKey: 'tradingDay',
    title: '交易日',
    width: 100,
    sortable: true,
  },
];

// 产品特有的列
const productColumn: ColumnDefinition<TradeRecordInfo>[0] = {
  key: 'accountName',
  dataKey: 'accountName',
  title: '账户',
  width: 120,
  sortable: true,
};

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.push(productColumn);
  }
  return cols;
});

// 历史成交数据
const historyRecords = shallowRef<TradeRecordInfo[]>([]);
const tableRef = useTemplateRef('tableRef');

// 日期范围
const dateRange = shallowRef<[Date, Date] | null>(null);

// 获取历史成交数据
const fetchHistoryRecords = async () => {
  if (!activeItem) return;

  // 模拟历史成交数据
  const mockData: TradeRecordInfo[] = [
    {
      tradeId: 'T2024011500001',
      orderId: 1001,
      instrument: '600036',
      instrumentName: '招商银行',
      direction: TradeDirectionEnum.买入,
      positionEffect: PositionEffectEnum.开仓,
      volume: 1000,
      tradedPrice: 31.48,
      tradedAmount: 31480,
      commission: 15.74,
      tradeTime: '2024-01-15 09:30:15',
      tradingDay: '2024-01-15',
      accountName: type === 'product' ? '测试账户1' : undefined,
    },
    {
      tradeId: 'T2024011400001',
      orderId: 1002,
      instrument: '000001',
      instrumentName: '平安银行',
      direction: TradeDirectionEnum.卖出,
      positionEffect: PositionEffectEnum.平仓,
      volume: 500,
      tradedPrice: 12.75,
      tradedAmount: 6375,
      commission: 3.19,
      tradeTime: '2024-01-14 14:25:30',
      tradingDay: '2024-01-14',
      accountName: type === 'product' ? '测试账户1' : undefined,
    },
  ];

  historyRecords.value = mockData;
};

// 导出数据
const exportData = () => {
  // 实现导出功能
  console.log('导出历史成交数据');
};

// 计算统计信息
const statistics = computed(() => {
  const records = historyRecords.value;
  const totalVolume = records.reduce((sum, record) => sum + record.volume, 0);
  const totalAmount = records.reduce((sum, record) => sum + record.tradedAmount, 0);
  const totalCommission = records.reduce((sum, record) => sum + record.commission, 0);

  return {
    totalVolume,
    totalAmount,
    totalCommission,
    recordCount: records.length,
  };
});

onMounted(() => {
  if (activeItem) {
    fetchHistoryRecords();
  }
});

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchHistoryRecords();
    }
  },
  { deep: true },
);

// 监听日期范围变化
watch(dateRange, () => {
  fetchHistoryRecords();
});
</script>

<template>
  <div flex="~ col" h-full>
    <VirtualizedTable
      ref="tableRef"
      :sort="{ key: 'tradeTime', order: TableV2SortOrder.DESC }"
      :columns
      :data="historyRecords"
      flex-1
      min-h-1
      fixed
    >
      <template #left>
        <div flex aic>
          <span mr-8 text-sm>日期范围:</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            w-240
            mr-16
          />
        </div>
      </template>
      <template #actions>
        <div class="actions" flex aic>
          <el-button @click="fetchHistoryRecords" size="small" color="var(--g-primary)">
            查询
          </el-button>
          <el-button @click="exportData" size="small">导出</el-button>
        </div>
      </template>
    </VirtualizedTable>

    <!-- 统计信息 -->
    <div class="statistics" p-16 border-t="1 solid [--g-border]" bg="[--g-panel-bg2]">
      <div flex aic gap-32>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>成交笔数:</span>
          <span font-medium>{{ statistics.recordCount }}</span>
        </div>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>总成交量:</span>
          <span font-medium>{{ statistics.totalVolume.toLocaleString() }}</span>
        </div>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>总成交金额:</span>
          <span font-medium>{{ formatNumber(statistics.totalAmount, 2) }}</span>
        </div>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>总手续费:</span>
          <span font-medium>{{ formatNumber(statistics.totalCommission, 2) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
