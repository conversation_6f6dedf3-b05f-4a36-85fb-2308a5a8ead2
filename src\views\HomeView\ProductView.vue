<script setup lang="ts">
import ProductList from '@/components/ProductView/ProductList.vue';
import ComponentTabs from '@/components/common/ComponentTabs.vue';
import type { ComponentTab } from '@/types';
import { computed, ref } from 'vue';
import type { LegacyFundInfo } from '../../../../xtrade-sdk/dist';

const activeFund = ref<LegacyFundInfo | null>(null);

const tabs = computed<ComponentTab[]>(() => [
  {
    label: '概览',
    component: 'ProductOverview',
    props: {
      activeItem: activeFund.value,
    },
  },
  {
    label: '当日持仓',
    component: 'CommonPositions',
    props: {
      activeItem: activeFund.value,
      type: 'product',
    },
  },
  {
    label: '当日订单',
    component: 'CommonOrders',
    props: {
      activeItem: activeFund.value,
      type: 'product',
    },
  },
  {
    label: '当日成交',
    component: 'CommonRecords',
    props: {
      activeItem: activeFund.value,
      type: 'product',
    },
  },
  {
    label: '历史订单',
    component: 'HistoryOrders',
    props: {
      activeItem: activeFund.value,
      type: 'product',
    },
  },
  {
    label: '历史持仓',
    component: 'HistoryPositions',
    props: {
      activeItem: activeFund.value,
      type: 'product',
    },
  },
  {
    label: '历史成交',
    component: 'HistoryRecords',
    props: {
      activeItem: activeFund.value,
      type: 'product',
    },
  },
  {
    label: '历史权益',
    component: 'HistoryEquity',
    props: {
      activeItem: activeFund.value,
      type: 'product',
    },
  },
]);

const handleRowClick = (row: LegacyFundInfo) => {
  activeFund.value = row;
};
</script>

<template>
  <div>
    <el-splitter layout="vertical">
      <el-splitter-panel>
        <ProductList h-full @row-click="handleRowClick" />
      </el-splitter-panel>
      <el-splitter-panel>
        <ComponentTabs :tabs h-full />
      </el-splitter-panel>
    </el-splitter>
  </div>
</template>

<style scoped></style>
