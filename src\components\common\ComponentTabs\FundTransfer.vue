<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import { formatNumber } from '@/script/formatter';
import type { LegacyFundInfo } from '../../../../../xtrade-sdk';

// 出入金记录接口
interface FundTransferRecord {
  id: number;
  transferId: string;
  type: 'deposit' | 'withdraw';
  amount: number;
  status: 'pending' | 'success' | 'failed';
  bankAccount: string;
  bankName: string;
  transferTime: string;
  processTime?: string;
  remark?: string;
  accountName?: string;
}

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
}>();

// 出入金类型选项
const transferTypes = [
  { label: '全部', value: '' },
  { label: '入金', value: 'deposit' },
  { label: '出金', value: 'withdraw' },
];

// 状态选项
const statusOptions = [
  { label: '全部', value: '' },
  { label: '处理中', value: 'pending' },
  { label: '成功', value: 'success' },
  { label: '失败', value: 'failed' },
];

// 基础列定义
const baseColumns: ColumnDefinition<FundTransferRecord> = [
  {
    key: 'transferId',
    dataKey: 'transferId',
    title: '流水号',
    width: 120,
    sortable: true,
    fixed: true,
  },
  {
    key: 'type',
    dataKey: 'type',
    title: '类型',
    width: 80,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      const typeText = cellData === 'deposit' ? '入金' : '出金';
      const colorClass = cellData === 'deposit' ? 'c-[var(--g-green)]' : 'c-[var(--g-red)]';
      return <span class={colorClass}>{typeText}</span>;
    },
  },
  {
    key: 'amount',
    dataKey: 'amount',
    title: '金额',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData, rowData }) => {
      const colorClass = rowData.type === 'deposit' ? 'c-[var(--g-green)]' : 'c-[var(--g-red)]';
      const prefix = rowData.type === 'deposit' ? '+' : '-';
      return (
        <span class={colorClass}>
          {prefix}
          {formatNumber(cellData, 2)}
        </span>
      );
    },
  },
  {
    key: 'status',
    dataKey: 'status',
    title: '状态',
    width: 80,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      const statusMap = {
        pending: { text: '处理中', color: 'c-[var(--g-orange)]' },
        success: { text: '成功', color: 'c-[var(--g-green)]' },
        failed: { text: '失败', color: 'c-[var(--g-red)]' },
      };
      const status = statusMap[cellData];
      return <span class={status.color}>{status.text}</span>;
    },
  },
  {
    key: 'bankName',
    dataKey: 'bankName',
    title: '银行',
    width: 120,
    sortable: true,
  },
  {
    key: 'bankAccount',
    dataKey: 'bankAccount',
    title: '银行账号',
    width: 150,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      // 隐藏部分账号信息
      const masked = cellData.replace(/(\d{4})\d+(\d{4})/, '$1****$2');
      return <span>{masked}</span>;
    },
  },
  {
    key: 'transferTime',
    dataKey: 'transferTime',
    title: '申请时间',
    width: 160,
    sortable: true,
  },
  {
    key: 'processTime',
    dataKey: 'processTime',
    title: '处理时间',
    width: 160,
    sortable: true,
    cellRenderer: ({ cellData }) => cellData || '--',
  },
  {
    key: 'remark',
    dataKey: 'remark',
    title: '备注',
    width: 200,
    cellRenderer: ({ cellData }) => cellData || '--',
  },
];

// 产品特有的列
const productColumn: ColumnDefinition<FundTransferRecord>[0] = {
  key: 'accountName',
  dataKey: 'accountName',
  title: '账户',
  width: 120,
  sortable: true,
};

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.push(productColumn);
  }
  return cols;
});

// 出入金数据
const fundTransfers = shallowRef<FundTransferRecord[]>([]);
const tableRef = useTemplateRef('tableRef');

// 筛选条件
const transferType = shallowRef('');
const transferStatus = shallowRef('');
const dateRange = shallowRef<[Date, Date] | null>(null);

// 数据过滤
const filteredData = computed(() => {
  let data = fundTransfers.value;

  if (transferType.value) {
    data = data.filter(item => item.type === transferType.value);
  }

  if (transferStatus.value) {
    data = data.filter(item => item.status === transferStatus.value);
  }

  return data;
});

// 获取出入金数据
const fetchFundTransfers = async () => {
  if (!activeItem) return;

  // 模拟出入金数据
  const mockData: FundTransferRecord[] = [
    {
      id: 1,
      transferId: 'TF202401150001',
      type: 'deposit',
      amount: 100000,
      status: 'success',
      bankAccount: '****************',
      bankName: '工商银行',
      transferTime: '2024-01-15 09:30:00',
      processTime: '2024-01-15 09:35:00',
      remark: '初始入金',
      accountName: type === 'product' ? '测试账户1' : undefined,
    },
    {
      id: 2,
      transferId: 'TF202401140001',
      type: 'withdraw',
      amount: 50000,
      status: 'success',
      bankAccount: '****************',
      bankName: '工商银行',
      transferTime: '2024-01-14 15:20:00',
      processTime: '2024-01-14 15:25:00',
      remark: '部分出金',
      accountName: type === 'product' ? '测试账户1' : undefined,
    },
    {
      id: 3,
      transferId: 'TF202401130001',
      type: 'deposit',
      amount: 200000,
      status: 'pending',
      bankAccount: '****************',
      bankName: '工商银行',
      transferTime: '2024-01-13 14:00:00',
      remark: '追加入金',
      accountName: type === 'product' ? '测试账户1' : undefined,
    },
  ];

  fundTransfers.value = mockData;
};

// 导出数据
const exportData = () => {
  // 实现导出功能
  console.log('导出出入金数据');
};

// 计算统计信息
const statistics = computed(() => {
  const data = filteredData.value;
  const deposits = data.filter(item => item.type === 'deposit' && item.status === 'success');
  const withdraws = data.filter(item => item.type === 'withdraw' && item.status === 'success');

  const totalDeposit = deposits.reduce((sum, item) => sum + item.amount, 0);
  const totalWithdraw = withdraws.reduce((sum, item) => sum + item.amount, 0);
  const netAmount = totalDeposit - totalWithdraw;

  return {
    totalDeposit,
    totalWithdraw,
    netAmount,
    depositCount: deposits.length,
    withdrawCount: withdraws.length,
    totalCount: data.length,
  };
});

onMounted(() => {
  if (activeItem) {
    fetchFundTransfers();
  }
});

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchFundTransfers();
    }
  },
  { deep: true },
);

// 监听筛选条件变化
watch([transferType, transferStatus, dateRange], () => {
  fetchFundTransfers();
});
</script>

<template>
  <div flex="~ col" h-full>
    <VirtualizedTable
      ref="tableRef"
      :sort="{ key: 'transferTime', order: TableV2SortOrder.DESC }"
      :columns
      :data="filteredData"
      flex-1
      min-h-1
      fixed
    >
      <template #left>
        <div flex aic>
          <span mr-8 text-sm>类型:</span>
          <el-select v-model="transferType" placeholder="选择类型" size="small" w-100 mr-16>
            <el-option
              v-for="option in transferTypes"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <span mr-8 text-sm>状态:</span>
          <el-select v-model="transferStatus" placeholder="选择状态" size="small" w-100 mr-16>
            <el-option
              v-for="option in statusOptions"
              :key="option.value"
              :label="option.label"
              :value="option.value"
            />
          </el-select>

          <span mr-8 text-sm>日期:</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            w-240
          />
        </div>
      </template>
      <template #actions>
        <div class="actions" flex aic>
          <el-button @click="fetchFundTransfers" size="small" color="var(--g-primary)">
            查询
          </el-button>
          <el-button @click="exportData" size="small">导出</el-button>
        </div>
      </template>
    </VirtualizedTable>

    <!-- 统计信息 -->
    <div class="statistics" p-16 border-t="1 solid [--g-border]" bg="[--g-panel-bg2]">
      <div flex aic gap-32>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>总笔数:</span>
          <span font-medium>{{ statistics.totalCount }}</span>
        </div>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>入金总额:</span>
          <span font-medium c-green>{{ formatNumber(statistics.totalDeposit, 2) }}</span>
          <span text-xs text-gray-500 ml-4>({{ statistics.depositCount }}笔)</span>
        </div>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>出金总额:</span>
          <span font-medium c-red>{{ formatNumber(statistics.totalWithdraw, 2) }}</span>
          <span text-xs text-gray-500 ml-4>({{ statistics.withdrawCount }}笔)</span>
        </div>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>净流入:</span>
          <span
            font-medium
            :class="
              statistics.netAmount > 0
                ? 'c-[var(--g-green)]'
                : statistics.netAmount < 0
                  ? 'c-[var(--g-red)]'
                  : 'c-[var(--g-white)]'
            "
          >
            {{ formatNumber(statistics.netAmount, 2) }}
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
