<script setup lang="ts" generic="T extends 'account' | 'product'">
import { computed, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { TableV2SortOrder } from 'element-plus';
import VirtualizedTable from '../VirtualizedTable.vue';
import type { AccountInfo, ColumnDefinition } from '@/types';
import { formatNumber } from '@/script/formatter';
import type { LegacyFundInfo } from '../../../../../xtrade-sdk';

// 权益记录接口
interface EquityRecord {
  id: number;
  date: string;
  totalAssets: number;
  availableFunds: number;
  frozenFunds: number;
  positionValue: number;
  todayPnL: number;
  totalPnL: number;
  nav?: number; // 产品净值
  accountName?: string;
}

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
}>();

// 基础列定义
const baseColumns: ColumnDefinition<EquityRecord> = [
  {
    key: 'date',
    dataKey: 'date',
    title: '日期',
    width: 100,
    sortable: true,
    fixed: true,
  },
  {
    key: 'totalAssets',
    dataKey: 'totalAssets',
    title: '总资产',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => formatNumber(cellData, 2),
  },
  {
    key: 'availableFunds',
    dataKey: 'availableFunds',
    title: '可用资金',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => formatNumber(cellData, 2),
  },
  {
    key: 'frozenFunds',
    dataKey: 'frozenFunds',
    title: '冻结资金',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => formatNumber(cellData, 2),
  },
  {
    key: 'positionValue',
    dataKey: 'positionValue',
    title: '持仓市值',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => formatNumber(cellData, 2),
  },
  {
    key: 'todayPnL',
    dataKey: 'todayPnL',
    title: '当日盈亏',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => {
      const colorClass =
        cellData > 0
          ? 'c-[var(--g-green)]'
          : cellData < 0
            ? 'c-[var(--g-red)]'
            : 'c-[var(--g-white)]';
      return <span class={colorClass}>{formatNumber(cellData, 2)}</span>;
    },
  },
  {
    key: 'totalPnL',
    dataKey: 'totalPnL',
    title: '累计盈亏',
    width: 120,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => {
      const colorClass =
        cellData > 0
          ? 'c-[var(--g-green)]'
          : cellData < 0
            ? 'c-[var(--g-red)]'
            : 'c-[var(--g-white)]';
      return <span class={colorClass}>{formatNumber(cellData, 2)}</span>;
    },
  },
];

// 产品特有的列
const productColumns: ColumnDefinition<EquityRecord> = [
  {
    key: 'nav',
    dataKey: 'nav',
    title: '净值',
    width: 100,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => formatNumber(cellData, 4),
  },
  {
    key: 'accountName',
    dataKey: 'accountName',
    title: '账户',
    width: 120,
    sortable: true,
  },
];

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.splice(2, 0, productColumns[0]); // 在总资产前插入净值列
    cols.push(productColumns[1]); // 添加账户列
  }
  return cols;
});

// 历史权益数据
const historyEquity = shallowRef<EquityRecord[]>([]);
const tableRef = useTemplateRef('tableRef');

// 日期范围
const dateRange = shallowRef<[Date, Date] | null>(null);

// 获取历史权益数据
const fetchHistoryEquity = async () => {
  if (!activeItem) return;

  // 模拟历史权益数据
  const mockData: EquityRecord[] = [];
  const startDate = new Date('2024-01-01');
  const endDate = new Date('2024-01-15');

  for (let d = new Date(startDate); d <= endDate; d.setDate(d.getDate() + 1)) {
    // 跳过周末
    if (d.getDay() === 0 || d.getDay() === 6) continue;

    const baseAssets = 1000000;
    const randomFactor = 0.95 + Math.random() * 0.1; // 0.95-1.05的随机因子
    const totalAssets = baseAssets * randomFactor;
    const positionValue = totalAssets * 0.7;
    const availableFunds = totalAssets - positionValue;
    const todayPnL = (Math.random() - 0.5) * 20000; // -10000到10000的随机盈亏

    mockData.push({
      id: mockData.length + 1,
      date: d.toISOString().split('T')[0],
      totalAssets,
      availableFunds,
      frozenFunds: Math.random() * 10000,
      positionValue,
      todayPnL,
      totalPnL: (randomFactor - 1) * baseAssets,
      nav: type === 'product' ? randomFactor : undefined,
      accountName: type === 'product' ? '测试账户1' : undefined,
    });
  }

  historyEquity.value = mockData.reverse(); // 最新日期在前
};

// 导出数据
const exportData = () => {
  // 实现导出功能
  console.log('导出历史权益数据');
};

// 计算统计信息
const statistics = computed(() => {
  const records = historyEquity.value;
  if (records.length === 0) return null;

  const latest = records[0];
  const earliest = records[records.length - 1];
  const totalReturn = latest.totalAssets - earliest.totalAssets;
  const returnRate = totalReturn / earliest.totalAssets;

  return {
    totalReturn,
    returnRate,
    maxAssets: Math.max(...records.map(r => r.totalAssets)),
    minAssets: Math.min(...records.map(r => r.totalAssets)),
    recordCount: records.length,
  };
});

onMounted(() => {
  if (activeItem) {
    fetchHistoryEquity();
  }
});

// 监听activeItem变化
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchHistoryEquity();
    }
  },
  { deep: true },
);

// 监听日期范围变化
watch(dateRange, () => {
  fetchHistoryEquity();
});
</script>

<template>
  <div flex="~ col" h-full>
    <VirtualizedTable
      ref="tableRef"
      :sort="{ key: 'date', order: TableV2SortOrder.DESC }"
      :columns
      :data="historyEquity"
      flex-1
      min-h-1
      fixed
    >
      <template #left>
        <div flex aic>
          <span mr-8 text-sm>日期范围:</span>
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            size="small"
            w-240
            mr-16
          />
        </div>
      </template>
      <template #actions>
        <div class="actions" flex aic>
          <el-button @click="fetchHistoryEquity" size="small" color="var(--g-primary)">
            查询
          </el-button>
          <el-button @click="exportData" size="small">导出</el-button>
        </div>
      </template>
    </VirtualizedTable>

    <!-- 统计信息 -->
    <div
      v-if="statistics"
      class="statistics"
      p-16
      border-t="1 solid [--g-border]"
      bg="[--g-panel-bg2]"
    >
      <div flex aic gap-32>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>记录数:</span>
          <span font-medium>{{ statistics.recordCount }}</span>
        </div>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>总收益:</span>
          <span
            font-medium
            :class="
              statistics.totalReturn > 0
                ? 'c-[var(--g-green)]'
                : statistics.totalReturn < 0
                  ? 'c-[var(--g-red)]'
                  : 'c-[var(--g-white)]'
            "
          >
            {{ formatNumber(statistics.totalReturn, 2) }}
          </span>
        </div>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>收益率:</span>
          <span
            font-medium
            :class="
              statistics.returnRate > 0
                ? 'c-[var(--g-green)]'
                : statistics.returnRate < 0
                  ? 'c-[var(--g-red)]'
                  : 'c-[var(--g-white)]'
            "
          >
            {{ (statistics.returnRate * 100).toFixed(2) }}%
          </span>
        </div>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>最高资产:</span>
          <span font-medium>{{ formatNumber(statistics.maxAssets, 2) }}</span>
        </div>
        <div flex aic>
          <span text-sm text-gray-400 mr-8>最低资产:</span>
          <span font-medium>{{ formatNumber(statistics.minAssets, 2) }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
