<script setup lang="tsx" generic="T extends 'account' | 'product'">
import { computed, onBeforeUnmount, onMounted, shallowRef, useTemplateRef, watch } from 'vue';
import { ElMessage } from 'element-plus';
import { formatNumber, getColorClass } from '@/script/formatter';
import { putRow } from '@/script';
import { PositionDirectionEnum } from '@/enum';
import type { AccountInfo, ColumnDefinition } from '@/types';
import VirtualizedTable from '../VirtualizedTable.vue';
import { RecordService, TradingService } from '@/api';
import type { PositionInfo, SocketDataPackage, LegacyFundInfo } from '../../../../../xtrade-sdk';

// 定义组件属性
const { type, activeItem } = defineProps<{
  type: T;
  activeItem?: T extends 'account' ? AccountInfo : LegacyFundInfo;
}>();

// 基础列定义
const baseColumns: ColumnDefinition<PositionInfo> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '代码',
    width: 200,
    sortable: true,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '名称',
    width: 200,
    sortable: true,
  },
  {
    key: 'direction',
    dataKey: 'direction',
    title: '方向',
    width: 120,
    sortable: true,
    cellRenderer: ({ cellData }) => {
      const dirText = cellData === PositionDirectionEnum.多头 ? '买入' : '卖出';
      return <span class={getColorClass(cellData)}>{dirText}</span>;
    },
  },
  {
    key: 'todayPosition',
    dataKey: 'todayPosition',
    title: '数量',
    width: 160,
    sortable: true,
    align: 'right',
  },
  {
    key: 'avgPrice',
    dataKey: 'avgPrice',
    title: '均价',
    width: 160,
    sortable: true,
    align: 'right',
  },
  {
    key: 'lastSettlePrice',
    dataKey: 'lastSettlePrice',
    title: '现价',
    width: 160,
    sortable: true,
    align: 'right',
  },
  {
    key: 'floatProfit',
    dataKey: 'floatProfit',
    title: '浮动盈亏',
    width: 200,
    sortable: true,
    align: 'right',
    cellRenderer: ({ cellData }) => {
      const colorClass = getColorClass(cellData);
      return <span class={colorClass}>{formatNumber(cellData)}</span>;
    },
  },
  {
    key: 'marketValue',
    dataKey: 'marketValue',
    title: '市值',
    width: 200,
    sortable: true,
    align: 'right',
  },
  {
    key: 'usedMargin',
    dataKey: 'usedMargin',
    title: '保证金',
    width: 160,
    sortable: true,
    align: 'right',
  },
  {
    key: 'usedCommission',
    dataKey: 'usedCommission',
    title: '手续费',
    width: 160,
    sortable: true,
    align: 'right',
  },
];

// 产品特有的列
const productColumn: ColumnDefinition<PositionInfo>[0] = {
  key: 'accountName',
  dataKey: 'accountName',
  title: '账户',
  width: 120,
  sortable: true,
};

// 根据类型动态生成列
const columns = computed(() => {
  const cols = [...baseColumns];
  if (type === 'product') {
    cols.push(productColumn);
  }
  return cols;
});

const tableRef = useTemplateRef('tableRef');
const positions = shallowRef<PositionInfo[]>([]);

// 监听账户/产品变化，重新获取持仓数据
watch(
  () => activeItem,
  newItem => {
    if (newItem) {
      fetchPositions();
    }
  },
  { deep: true },
);

// 初始化数据
onMounted(() => {
  if (activeItem) {
    fetchPositions();
  }
  TradingService.subscribePositionChange(handlePositionChange);
});

onBeforeUnmount(() => {
  TradingService.unsubscribePositionChange(handlePositionChange);
});

/** 监听持仓变化 */
const handlePositionChange = (data: SocketDataPackage<PositionInfo>) => {
  const { body } = data;
  if (body) {
    putRow(body, positions, 'instrument');
  }
};

// 获取持仓数据
const fetchPositions = async () => {
  if (!activeItem) return;
  positions.value = await RecordService.getTodayPositions(activeItem.id);
};

// 平仓选中的持仓
const closeSelectedPositions = () => {
  if (tableRef.value?.selectedRows.length === 0) {
    ElMessage.warning('请选择持仓');
    return;
  }

  ElMessage.warning('待实现');
  // TODO: 实现平仓逻辑
  console.log('平仓选中的持仓:', tableRef.value?.selectedRows);
};
</script>

<template>
  <VirtualizedTable
    ref="tableRef"
    :columns="columns"
    :data="positions"
    identity="instrument"
    select
  >
    <template #actions>
      <div class="actions" flex aic>
        <el-button @click="fetchPositions" size="small" color="var(--g-primary)">刷新</el-button>
        <el-button
          @click="closeSelectedPositions"
          size="small"
          color="var(--g-danger)"
          :disabled="!tableRef?.selectedRows.length"
        >
          平仓
        </el-button>
      </div>
    </template>
  </VirtualizedTable>
</template>

<style scoped></style>
